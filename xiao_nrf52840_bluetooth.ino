#include <bluefruit.h>

// BLE Service and Characteristic UUIDs
#define SERVICE_UUID        0x1234
#define CHARACTERISTIC_UUID 0x5678

// BLE Service and Characteristic
BLEService sensorService = BLEService(SERVICE_UUID);
BLECharacteristic sensorCharacteristic = BLECharacteristic(CHARACTERISTIC_UUID);

// Serial communication from Feather M0
#define FEATHER_RX 0  // RX from Feather TX
#define FEATHER_TX 1  // TX to Feather RX (not used for receiving)

// Data packet structure (matches Feather M0 output)
struct DataPacket {
  uint8_t header1;
  uint8_t header2;
  uint32_t timestamp;
  int16_t angular_velocity;
  int16_t angular_velocity_x;
  int16_t angular_velocity_y;
  int16_t angular_velocity_z;
  int16_t accel_x;
  int16_t accel_y;
  int16_t accel_z;
};

// Buffer for incoming serial data
uint8_t serialBuffer[20];
int bufferIndex = 0;
bool packetReady = false;

void setup() {
  Serial.begin(115200);  // Debug serial
  Serial1.begin(57600);  // Communication with Feather M0

  // Initialize Bluefruit
  Bluefruit.begin();
  Bluefruit.setTxPower(4);    // Check bluefruit.h for supported values
  Bluefruit.setName("XIAO-SensorHub");

  // Configure and Start BLE Uart Service
  sensorService.begin();

  // Set up characteristics
  sensorCharacteristic.setProperties(CHR_PROPS_READ | CHR_PROPS_NOTIFY);
  sensorCharacteristic.setPermission(SECMODE_OPEN, SECMODE_NO_ACCESS);
  sensorCharacteristic.setFixedLen(20);
  sensorCharacteristic.begin();

  // Start advertising
  startAdv();

  Serial.println("✅ XIAO nRF52840 BLE Sensor Hub Ready!");
  Serial.println("Waiting for BLE connections...");
}

void loop() {
  // Check for BLE connections
  if (Bluefruit.connected()) {
    // While connected, process serial data and send via BLE
    readSerialData();

    if (packetReady) {
      sendDataViaBLE();
      packetReady = false;
      bufferIndex = 0;
    }
  } else {
    // If not connected, still read serial data but don't send
    readSerialData();
    if (packetReady) {
      Serial.println("Data received but no BLE connection");
      packetReady = false;
      bufferIndex = 0;
    }
  }

  delay(10); // Small delay to prevent overwhelming the BLE stack
}

void readSerialData() {
  while (Serial1.available() && bufferIndex < 20) {
    uint8_t incomingByte = Serial1.read();
    
    // Look for header pattern (0xAA, 0xAA)
    if (bufferIndex == 0) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
      }
    } else if (bufferIndex == 1) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
      } else {
        bufferIndex = 0; // Reset if second byte is not 0xAA
      }
    } else {
      serialBuffer[bufferIndex++] = incomingByte;
      
      // Check if we have a complete packet (20 bytes total)
      if (bufferIndex >= 20) {
        packetReady = true;
        break;
      }
    }
  }
}

void sendDataViaBLE() {
  // Send the raw packet data via BLE
  if (Bluefruit.connected()) {
    sensorCharacteristic.notify(serialBuffer, 20);

    // Debug: Print received data
    DataPacket* packet = (DataPacket*)serialBuffer;
    Serial.print("Sent via BLE - Timestamp: ");
    Serial.print(packet->timestamp);
    Serial.print(", Angular Vel: ");
    Serial.print(packet->angular_velocity / 100.0);
    Serial.print(", Accel X: ");
    Serial.print(packet->accel_x / 100.0);
    Serial.print(", Y: ");
    Serial.print(packet->accel_y / 100.0);
    Serial.print(", Z: ");
    Serial.println(packet->accel_z / 100.0);
  }
}

// Function to check BLE connection status
bool isBLEConnected() {
  return Bluefruit.connected();
}

// Function to get signal strength (if needed for debugging)
int getBLESignalStrength() {
  if (Bluefruit.connected()) {
    return Bluefruit.rssi();
  }
  return 0;
}

// Start advertising
void startAdv(void) {
  // Advertising packet
  Bluefruit.Advertising.addFlags(BLE_GAP_ADV_FLAGS_LE_ONLY_GENERAL_DISC_MODE);
  Bluefruit.Advertising.addTxPower();

  // Include the BLE UART service
  Bluefruit.Advertising.addService(sensorService);

  // Secondary Scan Response packet (optional)
  Bluefruit.ScanResponse.addName();

  /* Start Advertising
   * - Enable auto advertising if disconnected
   * - Interval:  fast mode = 20 ms, slow mode = 152.5 ms
   * - Timeout for fast mode is 30 seconds
   * - Start(timeout) with timeout = 0 will advertise forever (until connected)
   *
   * For recommended advertising interval
   * https://developer.apple.com/library/content/qa/qa1931/_index.html
   */
  Bluefruit.Advertising.restartOnDisconnect(true);
  Bluefruit.Advertising.setInterval(32, 244);    // in unit of 0.625 ms
  Bluefruit.Advertising.setFastTimeout(30);      // number of seconds in fast mode
  Bluefruit.Advertising.start(0);                // 0 = Don't stop advertising after n seconds
}
