#include <ArduinoBLE.h>

// BLE Service and Characteristic UUIDs
#define SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// BLE Service and Characteristic
BLEService sensorService(SERVICE_UUID);
BLECharacteristic sensorCharacteristic(CHARACTERISTIC_UUID, BLERead | BLENotify, 20);

// Serial communication from Feather M0
#define FEATHER_RX 0  // RX from Feather TX
#define FEATHER_TX 1  // TX to Feather RX (not used for receiving)

// Data packet structure (matches Feather M0 output)
struct DataPacket {
  uint8_t header1;
  uint8_t header2;
  uint32_t timestamp;
  int16_t angular_velocity;
  int16_t angular_velocity_x;
  int16_t angular_velocity_y;
  int16_t angular_velocity_z;
  int16_t accel_x;
  int16_t accel_y;
  int16_t accel_z;
};

// Buffer for incoming serial data
uint8_t serialBuffer[20];
int bufferIndex = 0;
bool packetReady = false;

void setup() {
  Serial.begin(115200);  // Debug serial
  Serial1.begin(57600);  // Communication with Feather M0
  
  // Initialize BLE
  if (!BLE.begin()) {
    Serial.println("❌ Starting BLE failed!");
    while (1);
  }
  
  // Set BLE device name and local name
  BLE.setLocalName("XIAO-SensorHub");
  BLE.setDeviceName("XIAO-SensorHub");
  
  // Add the service and characteristic
  BLE.setAdvertisedService(sensorService);
  sensorService.addCharacteristic(sensorCharacteristic);
  BLE.addService(sensorService);
  
  // Start advertising
  BLE.advertise();
  
  Serial.println("✅ XIAO nRF52840 BLE Sensor Hub Ready!");
  Serial.println("Waiting for BLE connections...");
}

void loop() {
  // Check for BLE connections
  BLEDevice central = BLE.central();
  
  if (central) {
    Serial.print("Connected to central: ");
    Serial.println(central.address());
    
    // While connected, process serial data and send via BLE
    while (central.connected()) {
      readSerialData();
      
      if (packetReady) {
        sendDataViaBLE();
        packetReady = false;
        bufferIndex = 0;
      }
      
      delay(10); // Small delay to prevent overwhelming the BLE stack
    }
    
    Serial.print("Disconnected from central: ");
    Serial.println(central.address());
  }
  
  // If not connected, still read serial data but don't send
  readSerialData();
  if (packetReady) {
    Serial.println("Data received but no BLE connection");
    packetReady = false;
    bufferIndex = 0;
  }
}

void readSerialData() {
  while (Serial1.available() && bufferIndex < 20) {
    uint8_t incomingByte = Serial1.read();
    
    // Look for header pattern (0xAA, 0xAA)
    if (bufferIndex == 0) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
      }
    } else if (bufferIndex == 1) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
      } else {
        bufferIndex = 0; // Reset if second byte is not 0xAA
      }
    } else {
      serialBuffer[bufferIndex++] = incomingByte;
      
      // Check if we have a complete packet (20 bytes total)
      if (bufferIndex >= 20) {
        packetReady = true;
        break;
      }
    }
  }
}

void sendDataViaBLE() {
  // Send the raw packet data via BLE
  sensorCharacteristic.writeValue(serialBuffer, 20);
  
  // Debug: Print received data
  DataPacket* packet = (DataPacket*)serialBuffer;
  Serial.print("Sent via BLE - Timestamp: ");
  Serial.print(packet->timestamp);
  Serial.print(", Angular Vel: ");
  Serial.print(packet->angular_velocity / 100.0);
  Serial.print(", Accel X: ");
  Serial.print(packet->accel_x / 100.0);
  Serial.print(", Y: ");
  Serial.print(packet->accel_y / 100.0);
  Serial.print(", Z: ");
  Serial.println(packet->accel_z / 100.0);
}

// Function to check BLE connection status
bool isBLEConnected() {
  BLEDevice central = BLE.central();
  return central && central.connected();
}

// Function to get signal strength (if needed for debugging)
int getBLESignalStrength() {
  BLEDevice central = BLE.central();
  if (central && central.connected()) {
    return central.rssi();
  }
  return 0;
}
