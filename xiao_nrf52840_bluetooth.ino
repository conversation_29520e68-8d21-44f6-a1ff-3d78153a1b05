#include <bluefruit.h>

// BLE Service and Characteristic UUIDs (must match Python code)
uint8_t serviceUUID[16] = {0xbc, 0x9a, 0x78, 0x56, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x78, 0x56, 0x34, 0x12};
uint8_t characteristicUUID[16] = {0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x65, 0x87};

// BLE Service and Characteristic
BLEService sensorService = BLEService(serviceUUID);
BLECharacteristic sensorCharacteristic = BLECharacteristic(characteristicUUID);

// Serial communication from Feather M0
#define FEATHER_RX 0  // RX from Feather TX
#define FEATHER_TX 1  // TX to Feather RX (not used for receiving)

// Data packet structure (matches Feather M0 output)
struct DataPacket {
  uint8_t header1;
  uint8_t header2;
  uint32_t timestamp;
  int16_t angular_velocity;
  int16_t angular_velocity_x;
  int16_t angular_velocity_y;
  int16_t angular_velocity_z;
  int16_t accel_x;
  int16_t accel_y;
  int16_t accel_z;
};

// Buffer for incoming serial data
uint8_t serialBuffer[20];
int bufferIndex = 0;
bool packetReady = false;

void setup() {
  Serial.begin(115200);  // Debug serial
  Serial1.begin(57600);  // Communication with Feather M0

  // Initialize Bluefruit
  Bluefruit.begin();
  Bluefruit.setTxPower(4);    // Check bluefruit.h for supported values
  Bluefruit.setName("XIAO-SensorHub");

  // Configure and Start BLE Uart Service
  sensorService.begin();

  // Set up characteristics
  sensorCharacteristic.setProperties(CHR_PROPS_READ | CHR_PROPS_NOTIFY);
  sensorCharacteristic.setPermission(SECMODE_OPEN, SECMODE_NO_ACCESS);
  sensorCharacteristic.setFixedLen(20);
  sensorCharacteristic.begin();

  // Start advertising
  startAdv();

  Serial.println("✅ XIAO nRF52840 BLE Sensor Hub Ready!");
  Serial.println("Waiting for BLE connections...");
}

void loop() {
  static unsigned long lastStatusTime = 0;
  static bool lastConnectionState = false;

  // Debug: Print connection status changes
  bool currentConnectionState = Bluefruit.connected();
  if (currentConnectionState != lastConnectionState) {
    if (currentConnectionState) {
      Serial.println("🔗 BLE Connected!");
    } else {
      Serial.println("❌ BLE Disconnected!");
    }
    lastConnectionState = currentConnectionState;
  }

  // Debug: Print status every 10 seconds
  if (millis() - lastStatusTime > 10000) {
    Serial.print("Status - BLE Connected: ");
    Serial.print(currentConnectionState ? "YES" : "NO");
    Serial.print(", Buffer Index: ");
    Serial.print(bufferIndex);
    Serial.print(", Packet Ready: ");
    Serial.println(packetReady ? "YES" : "NO");
    lastStatusTime = millis();
  }

  // Check for BLE connections
  if (currentConnectionState) {
    // While connected, process serial data and send via BLE
    readSerialData();

    if (packetReady) {
      sendDataViaBLE();
      packetReady = false;
      bufferIndex = 0;
    }
  } else {
    // If not connected, still read serial data but don't send
    readSerialData();
    if (packetReady) {
      Serial.println("📦 Data received but no BLE connection");
      packetReady = false;
      bufferIndex = 0;
    }
  }

  delay(10); // Small delay to prevent overwhelming the BLE stack
}

void readSerialData() {
  static unsigned long lastDebugTime = 0;

  // Debug: Print if we're checking for serial data
  if (millis() - lastDebugTime > 5000) { // Every 5 seconds
    Serial.print("Checking serial... Available bytes: ");
    Serial.println(Serial1.available());
    lastDebugTime = millis();
  }

  while (Serial1.available() && bufferIndex < 20) {
    uint8_t incomingByte = Serial1.read();

    // Debug: Print received bytes
    Serial.print("Received byte ");
    Serial.print(bufferIndex);
    Serial.print(": 0x");
    Serial.println(incomingByte, HEX);

    // Look for header pattern (0xAA, 0xAA)
    if (bufferIndex == 0) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
        Serial.println("Found first header byte");
      }
    } else if (bufferIndex == 1) {
      if (incomingByte == 0xAA) {
        serialBuffer[bufferIndex++] = incomingByte;
        Serial.println("Found second header byte - packet started");
      } else {
        Serial.println("Invalid second header byte, resetting");
        bufferIndex = 0; // Reset if second byte is not 0xAA
      }
    } else {
      serialBuffer[bufferIndex++] = incomingByte;

      // Check if we have a complete packet (20 bytes total)
      if (bufferIndex >= 20) {
        packetReady = true;
        Serial.println("✅ Complete packet received!");
        break;
      }
    }
  }
}

void sendDataViaBLE() {
  Serial.println("📡 sendDataViaBLE() called");

  // Send the raw packet data via BLE
  if (Bluefruit.connected()) {
    Serial.println("🔗 BLE is connected, sending notification...");

    // Print raw buffer for debugging
    Serial.print("Raw buffer: ");
    for (int i = 0; i < 20; i++) {
      Serial.print("0x");
      Serial.print(serialBuffer[i], HEX);
      Serial.print(" ");
    }
    Serial.println();

    bool success = sensorCharacteristic.notify(serialBuffer, 20);
    Serial.print("Notification result: ");
    Serial.println(success ? "SUCCESS" : "FAILED");

    // Debug: Print received data
    DataPacket* packet = (DataPacket*)serialBuffer;
    Serial.print("Sent via BLE - Timestamp: ");
    Serial.print(packet->timestamp);
    Serial.print(", Angular Vel: ");
    Serial.print(packet->angular_velocity / 100.0);
    Serial.print(", Accel X: ");
    Serial.print(packet->accel_x / 100.0);
    Serial.print(", Y: ");
    Serial.print(packet->accel_y / 100.0);
    Serial.print(", Z: ");
    Serial.println(packet->accel_z / 100.0);
  } else {
    Serial.println("❌ BLE not connected, cannot send notification");
  }
}

// Function to check BLE connection status
bool isBLEConnected() {
  return Bluefruit.connected();
}

// Function to get signal strength (if needed for debugging)
int getBLESignalStrength() {
  if (Bluefruit.connected()) {
    // For Adafruit Bluefruit nRF52, RSSI is not easily accessible
    // Return a placeholder value or remove this function if not needed
    return -50; // Placeholder RSSI value
  }
  return 0;
}

// Start advertising
void startAdv(void) {
  // Advertising packet
  Bluefruit.Advertising.addFlags(BLE_GAP_ADV_FLAGS_LE_ONLY_GENERAL_DISC_MODE);
  Bluefruit.Advertising.addTxPower();

  // Include the BLE UART service
  Bluefruit.Advertising.addService(sensorService);

  // Secondary Scan Response packet (optional)
  Bluefruit.ScanResponse.addName();

  /* Start Advertising
   * - Enable auto advertising if disconnected
   * - Interval:  fast mode = 20 ms, slow mode = 152.5 ms
   * - Timeout for fast mode is 30 seconds
   * - Start(timeout) with timeout = 0 will advertise forever (until connected)
   *
   * For recommended advertising interval
   * https://developer.apple.com/library/content/qa/qa1931/_index.html
   */
  Bluefruit.Advertising.restartOnDisconnect(true);
  Bluefruit.Advertising.setInterval(32, 244);    // in unit of 0.625 ms
  Bluefruit.Advertising.setFastTimeout(30);      // number of seconds in fast mode
  Bluefruit.Advertising.start(0);                // 0 = Don't stop advertising after n seconds
}
