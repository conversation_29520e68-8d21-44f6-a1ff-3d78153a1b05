# Bluetooth IMU Sensor System

This system consists of three components that work together to collect, transmit, and visualize IMU sensor data wirelessly:

1. **Feather M0** - Collects sensor data and saves to SD card
2. **XIAO nRF52840** - Receives data via serial and transmits via Bluetooth
3. **Python Dashboard** - Receives Bluetooth data and displays real-time visualization

## Hardware Requirements

### Feather M0 Setup
- Adafruit Feather M0 with LoRa Radio
- Adafruit BNO055 IMU sensor
- MicroSD card and SD card breakout (or built-in SD)
- Connecting wires

**Wiring:**
- BNO055 SDA → Feather M0 SDA (Pin 20)
- BNO055 SCL → Feather M0 SCL (Pin 21)
- BNO055 VIN → Feather M0 3.3V
- BNO055 GND → Feather M0 GND
- SD Card CS → Feather M0 Pin 4
- Feather M0 TX (Pin 1) → XIAO RX (Pin 0)
- Feather M0 GND → XIAO GND

### XIAO nRF52840 Setup
- Seeed Studio XIAO nRF52840 (Sense)
- No additional components needed

**Wiring:**
- XIAO RX (Pin 0) → Feather M0 TX (Pin 1)
- <PERSON><PERSON><PERSON> GND → Feather M0 GND

### Computer Setup
- Windows/Mac/Linux computer with Bluetooth capability
- Python 3.8 or higher

## Software Installation

### 1. Arduino IDE Setup

Install the following libraries in Arduino IDE:
- Adafruit BNO055
- Adafruit Sensor
- ArduinoJson
- ArduinoBLE (for XIAO)

**Board Manager URLs:**
- ESP32/Feather M0: `https://adafruit.github.io/arduino-board-index/package_adafruit_index.json`
- XIAO nRF52840: `https://files.seeedstudio.com/arduino/package_seeedstudio_boards_index.json`

### 2. Upload Arduino Code

1. **Upload to Feather M0:**
   - Open `feather_m0_sensor.ino`
   - Select board: "Adafruit Feather M0"
   - Upload the code

2. **Upload to XIAO nRF52840:**
   - Open `xiao_nrf52840_bluetooth.ino`
   - Select board: "Seeed XIAO nRF52840 Sense"
   - Upload the code

### 3. Python Dashboard Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements_bluetooth.txt
   ```

2. **Run the dashboard:**
   ```bash
   python Sag9_bluetooth.py
   ```

## System Operation

### Data Flow
1. **Feather M0** reads IMU data from BNO055 sensor at 10Hz
2. Data is saved to SD card in CSV format
3. Same data is sent to XIAO via serial communication
4. **XIAO nRF52840** receives serial data and transmits via Bluetooth
5. **Python Dashboard** connects to XIAO via Bluetooth and displays real-time data

### Data Format
The system transmits the following sensor data:
- Timestamp (milliseconds)
- Angular Velocity (magnitude and X/Y/Z components)
- Accelerometer data (X/Y/Z axes)
- Calculated Roll, Pitch, Yaw angles

### Dashboard Features
- Real-time sensor data visualization
- Aircraft-style attitude indicators for orientation
- Trial recording functionality
- Data export to CSV files
- Automatic fallback to mock data if Bluetooth connection fails

## Troubleshooting

### Bluetooth Connection Issues
1. Ensure XIAO is powered and running
2. Check that computer Bluetooth is enabled
3. Try restarting the Python dashboard
4. Check Windows Bluetooth settings (may need to pair device first)

### Serial Communication Issues
1. Verify wiring between Feather M0 and XIAO
2. Check baud rate settings (should be 57600)
3. Ensure common ground connection

### Sensor Issues
1. Check BNO055 wiring and I2C connections
2. Verify sensor is detected during startup
3. Check power supply (3.3V for BNO055)

### SD Card Issues
1. Ensure SD card is properly formatted (FAT32)
2. Check CS pin connection
3. Verify SD card is not write-protected

## File Descriptions

- `feather_m0_sensor.ino` - Main sensor collection and SD logging code
- `xiao_nrf52840_bluetooth.ino` - Bluetooth transmission code
- `Sag9_bluetooth.py` - Python dashboard with Bluetooth connectivity
- `requirements_bluetooth.txt` - Python dependencies
- `README_Bluetooth_Setup.md` - This setup guide

## Data Output

### SD Card Data (CSV format)
```
Timestamp,AngularVel,AngularVelX,AngularVelY,AngularVelZ,AccelX,AccelY,AccelZ,Roll,Pitch,Yaw
1000,1.2345,0.1234,0.5678,0.9012,9.8100,0.1000,0.0500,5.2,1.8,45.6
```

### Trial Data
The dashboard can record specific trial periods and save them as separate CSV files with timestamps.

## Performance Notes

- Data collection rate: 10Hz (100ms intervals)
- Bluetooth transmission: Real-time
- Dashboard update rate: 10Hz
- SD card logging: Continuous
- Maximum data points displayed: 1000 (rolling window)

## Power Consumption

- Feather M0: ~50mA (with SD card writing)
- XIAO nRF52840: ~15mA (Bluetooth active)
- Total system: ~65mA at 3.7V (can run on battery)

## Future Enhancements

- Add GPS data logging
- Implement data compression for Bluetooth transmission
- Add wireless configuration interface
- Support for multiple sensor nodes
- Real-time data analysis and alerts
