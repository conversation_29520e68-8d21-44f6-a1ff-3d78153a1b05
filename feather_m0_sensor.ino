#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <SPI.h>
#include <SD.h>
#include <ArduinoJson.h>

// IMU Pins (I2C)
#define SDA_PIN 20  // Feather M0 SDA
#define SCL_PIN 21  // Feather M0 SCL

// SD Card Pins (SPI) - Feather M0 built-in SD
#define SD_CS 4     // Feather M0 SD CS pin

// Serial communication to XIAO (Hardware Serial1)
#define XIAO_TX 1   // TX to XIAO RX
#define XIAO_RX 0   // RX from XIAO TX

// Initialize BNO055 IMU
Adafruit_BNO055 bno = Adafruit_BNO055(55, 0x28);

// Data collection timing
unsigned long lastDataTime = 0;
const unsigned long dataInterval = 100; // 100ms = 10Hz data rate
int fileCounter = 1;

// Data structure for sensor readings
struct SensorData {
  unsigned long timestamp;
  float angular_velocity;
  float angular_velocity_x;
  float angular_velocity_y;
  float angular_velocity_z;
  float accel_x;
  float accel_y;
  float accel_z;
  float roll;
  float pitch;
  float yaw;
};

void setup() {
  Serial.begin(115200);  // Debug serial
  Serial1.begin(57600);  // Communication with XIAO
  
  // Initialize I2C for IMU
  Wire.begin();
  
  Serial.println("Initializing BNO055...");
  if (!bno.begin()) {
    Serial.println("❌ BNO055 not detected. Check wiring!");
    while (1);
  }
  Serial.println("✅ BNO055 detected successfully!");
  bno.setExtCrystalUse(true);
  delay(1000);
  
  // Initialize SD card
  if (!SD.begin(SD_CS)) {
    Serial.println("❌ SD Card initialization failed!");
    return;
  }
  Serial.println("✅ SD Card initialized successfully.");
  
  // Create CSV header if file doesn't exist
  File file = SD.open("sensor_data.csv", FILE_WRITE);
  if (file) {
    if (file.size() == 0) {
      file.println("Timestamp,AngularVel,AngularVelX,AngularVelY,AngularVelZ,AccelX,AccelY,AccelZ,Roll,Pitch,Yaw");
    }
    file.close();
  }
  
  Serial.println("Feather M0 Sensor Logger Ready!");
}

void loop() {
  if (millis() - lastDataTime >= dataInterval) {
    SensorData data = readSensorData();
    
    // Save to SD card
    saveToCsv(data);
    
    // Send to XIAO via serial
    sendToXiao(data);
    
    lastDataTime = millis();
  }
}

SensorData readSensorData() {
  SensorData data;
  
  // Get timestamp
  data.timestamp = millis();
  
  // Get orientation data
  sensors_event_t orientationData;
  bno.getEvent(&orientationData, Adafruit_BNO055::VECTOR_EULER);
  data.roll = orientationData.orientation.z;   // Roll
  data.pitch = orientationData.orientation.y;  // Pitch  
  data.yaw = orientationData.orientation.x;    // Yaw
  
  // Get angular velocity data
  sensors_event_t angVelData;
  bno.getEvent(&angVelData, Adafruit_BNO055::VECTOR_GYROSCOPE);
  data.angular_velocity_x = angVelData.gyro.x;
  data.angular_velocity_y = angVelData.gyro.y;
  data.angular_velocity_z = angVelData.gyro.z;
  
  // Calculate magnitude of angular velocity
  data.angular_velocity = sqrt(pow(data.angular_velocity_x, 2) + 
                              pow(data.angular_velocity_y, 2) + 
                              pow(data.angular_velocity_z, 2));
  
  // Get accelerometer data
  sensors_event_t accelData;
  bno.getEvent(&accelData, Adafruit_BNO055::VECTOR_ACCELEROMETER);
  data.accel_x = accelData.acceleration.x;
  data.accel_y = accelData.acceleration.y;
  data.accel_z = accelData.acceleration.z;
  
  return data;
}

void saveToCsv(SensorData data) {
  File file = SD.open("sensor_data.csv", FILE_WRITE);
  if (file) {
    file.print(data.timestamp); file.print(",");
    file.print(data.angular_velocity, 4); file.print(",");
    file.print(data.angular_velocity_x, 4); file.print(",");
    file.print(data.angular_velocity_y, 4); file.print(",");
    file.print(data.angular_velocity_z, 4); file.print(",");
    file.print(data.accel_x, 4); file.print(",");
    file.print(data.accel_y, 4); file.print(",");
    file.print(data.accel_z, 4); file.print(",");
    file.print(data.roll, 4); file.print(",");
    file.print(data.pitch, 4); file.print(",");
    file.println(data.yaw, 4);
    file.close();
  } else {
    Serial.println("Error writing to SD card");
  }
}

void sendToXiao(SensorData data) {
  // Create packet similar to original XBee format
  // Header bytes (2 identical bytes)
  Serial1.write(0xAA);
  Serial1.write(0xAA);
  
  // Runtime (4 bytes)
  Serial1.write((data.timestamp >> 24) & 0xFF);
  Serial1.write((data.timestamp >> 16) & 0xFF);
  Serial1.write((data.timestamp >> 8) & 0xFF);
  Serial1.write(data.timestamp & 0xFF);
  
  // Convert float data to 16-bit integers for transmission
  int16_t ang_vel = (int16_t)(data.angular_velocity * 100);
  int16_t ang_vel_x = (int16_t)(data.angular_velocity_x * 100);
  int16_t ang_vel_y = (int16_t)(data.angular_velocity_y * 100);
  int16_t ang_vel_z = (int16_t)(data.angular_velocity_z * 100);
  int16_t acc_x = (int16_t)(data.accel_x * 100);
  int16_t acc_y = (int16_t)(data.accel_y * 100);
  int16_t acc_z = (int16_t)(data.accel_z * 100);
  
  // Send sensor data (14 bytes)
  Serial1.write((ang_vel >> 8) & 0xFF);
  Serial1.write(ang_vel & 0xFF);
  Serial1.write((ang_vel_x >> 8) & 0xFF);
  Serial1.write(ang_vel_x & 0xFF);
  Serial1.write((ang_vel_y >> 8) & 0xFF);
  Serial1.write(ang_vel_y & 0xFF);
  Serial1.write((ang_vel_z >> 8) & 0xFF);
  Serial1.write(ang_vel_z & 0xFF);
  Serial1.write((acc_x >> 8) & 0xFF);
  Serial1.write(acc_x & 0xFF);
  Serial1.write((acc_y >> 8) & 0xFF);
  Serial1.write(acc_y & 0xFF);
  Serial1.write((acc_z >> 8) & 0xFF);
  Serial1.write(acc_z & 0xFF);
}
