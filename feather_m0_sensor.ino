#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <SPI.h>
#include <SD.h>
#include <ArduinoJson.h>

// IMU Pins (I2C)
#define SDA_PIN 20  // Feather M0 SDA
#define SCL_PIN 21  // Feather M0 SCL

// SD Card Pins (SPI) - Feather M0 built-in SD
#define SD_CS 4     // Feather M0 SD CS pin

// Serial communication to XIAO (Hardware Serial1)
#define XIAO_TX 1   // TX to XIAO RX
#define XIAO_RX 0   // RX from XIAO TX

// Button and LED Pins
#define BUTTON_PIN 5    // Button pin for trial control
#define LED_PIN 6      // Built-in LED pin (blue LED)

// Initialize BNO055 IMU
Adafruit_BNO055 bno = Adafruit_BNO055(55, 0x28);

// Data collection timing
unsigned long lastDataTime = 0;
const unsigned long dataInterval = 100; // 100ms = 10Hz data rate
int fileCounter = 1;

// Trial control variables
bool trialActive = false;
bool lastButtonState = HIGH;
bool buttonPressed = false;
unsigned long lastDebounceTime = 0;
const unsigned long debounceDelay = 50;

// LED control variables
unsigned long lastLedToggle = 0;
const unsigned long ledFlashInterval = 500; // 500ms flash interval
bool ledState = false;

// Current filename for data logging
String currentFilename = "";

// Data structure for sensor readings
struct SensorData {
  unsigned long timestamp;
  float angular_velocity;
  float angular_velocity_x;
  float angular_velocity_y;
  float angular_velocity_z;
  float accel_x;
  float accel_y;
  float accel_z;
  float roll;
  float pitch;
  float yaw;
};

void setup() {
  Serial.begin(115200);  // Debug serial
  Serial1.begin(57600);  // Communication with XIAO

  // Initialize button and LED pins
  pinMode(BUTTON_PIN, INPUT_PULLUP);
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);

  // Initialize I2C for IMU
  Wire.begin();

  Serial.println("Initializing BNO055...");
  if (!bno.begin()) {
    Serial.println("❌ BNO055 not detected. Check wiring!");
    while (1);
  }
  Serial.println("✅ BNO055 detected successfully!");
  bno.setExtCrystalUse(true);
  delay(1000);

  // Initialize SD card
  if (!SD.begin(SD_CS)) {
    Serial.println("❌ SD Card initialization failed!");
    while (1); // Don't continue without SD card
  }
  Serial.println("✅ SD Card initialized successfully.");

  // Initialize file counter based on existing files
  initializeFileCounter();

  Serial.println("Feather M0 Sensor Logger Ready!");
  Serial.println("Press button to start/stop trial recording");
  Serial.println("LED will flash when waiting, solid when recording");
}

void loop() {
  // Handle button input for trial control
  handleButtonInput();

  // Handle LED indication
  handleLedIndication();

  // Always collect and send data (for debugging), but only save to SD during trials
  if (millis() - lastDataTime >= dataInterval) {
    SensorData data = readSensorData();

    // Always send to XIAO for debugging
    sendToXiao(data);

    // Only save to SD card if trial is active
    if (trialActive) {
      saveToCsv(data);
    }

    lastDataTime = millis();
  }
}

SensorData readSensorData() {
  SensorData data;
  
  // Get timestamp
  data.timestamp = millis();
  
  // Get orientation data
  sensors_event_t orientationData;
  bno.getEvent(&orientationData, Adafruit_BNO055::VECTOR_EULER);
  data.roll = orientationData.orientation.z;   // Roll
  data.pitch = orientationData.orientation.y;  // Pitch  
  data.yaw = orientationData.orientation.x;    // Yaw
  
  // Get angular velocity data
  sensors_event_t angVelData;
  bno.getEvent(&angVelData, Adafruit_BNO055::VECTOR_GYROSCOPE);
  data.angular_velocity_x = angVelData.gyro.x;
  data.angular_velocity_y = angVelData.gyro.y;
  data.angular_velocity_z = angVelData.gyro.z;
  
  // Calculate magnitude of angular velocity
  data.angular_velocity = sqrt(pow(data.angular_velocity_x, 2) + 
                              pow(data.angular_velocity_y, 2) + 
                              pow(data.angular_velocity_z, 2));
  
  // Get accelerometer data
  sensors_event_t accelData;
  bno.getEvent(&accelData, Adafruit_BNO055::VECTOR_ACCELEROMETER);
  data.accel_x = accelData.acceleration.x;
  data.accel_y = accelData.acceleration.y;
  data.accel_z = accelData.acceleration.z;
  
  return data;
}

void saveToCsv(SensorData data) {
  if (currentFilename == "") {
    Serial.println("Error: No filename set for data logging");
    return;
  }

  File file = SD.open(currentFilename, FILE_WRITE);
  if (file) {
    file.print(data.timestamp); file.print(",");
    file.print(data.angular_velocity, 4); file.print(",");
    file.print(data.angular_velocity_x, 4); file.print(",");
    file.print(data.angular_velocity_y, 4); file.print(",");
    file.print(data.angular_velocity_z, 4); file.print(",");
    file.print(data.accel_x, 4); file.print(",");
    file.print(data.accel_y, 4); file.print(",");
    file.print(data.accel_z, 4); file.print(",");
    file.print(data.roll, 4); file.print(",");
    file.print(data.pitch, 4); file.print(",");
    file.println(data.yaw, 4);
    file.close();
  } else {
    Serial.println("Error writing to SD card: " + currentFilename);
  }
}

void sendToXiao(SensorData data) {
  static unsigned long lastDebugTime = 0;
  static int packetCount = 0;

  // Create packet similar to original XBee format
  // Header bytes (2 identical bytes)
  Serial1.write(0xAA);
  Serial1.write(0xAA);

  // Runtime (4 bytes)
  Serial1.write((data.timestamp >> 24) & 0xFF);
  Serial1.write((data.timestamp >> 16) & 0xFF);
  Serial1.write((data.timestamp >> 8) & 0xFF);
  Serial1.write(data.timestamp & 0xFF);

  // Convert float data to 16-bit integers for transmission
  int16_t ang_vel = (int16_t)(data.angular_velocity * 100);
  int16_t ang_vel_x = (int16_t)(data.angular_velocity_x * 100);
  int16_t ang_vel_y = (int16_t)(data.angular_velocity_y * 100);
  int16_t ang_vel_z = (int16_t)(data.angular_velocity_z * 100);
  int16_t acc_x = (int16_t)(data.accel_x * 100);
  int16_t acc_y = (int16_t)(data.accel_y * 100);
  int16_t acc_z = (int16_t)(data.accel_z * 100);

  // Send sensor data (14 bytes)
  Serial1.write((ang_vel >> 8) & 0xFF);
  Serial1.write(ang_vel & 0xFF);
  Serial1.write((ang_vel_x >> 8) & 0xFF);
  Serial1.write(ang_vel_x & 0xFF);
  Serial1.write((ang_vel_y >> 8) & 0xFF);
  Serial1.write(ang_vel_y & 0xFF);
  Serial1.write((ang_vel_z >> 8) & 0xFF);
  Serial1.write(ang_vel_z & 0xFF);
  Serial1.write((acc_x >> 8) & 0xFF);
  Serial1.write(acc_x & 0xFF);
  Serial1.write((acc_y >> 8) & 0xFF);
  Serial1.write(acc_y & 0xFF);
  Serial1.write((acc_z >> 8) & 0xFF);
  Serial1.write(acc_z & 0xFF);

  packetCount++;

  // Debug: Print packet info every 5 seconds
  if (millis() - lastDebugTime > 5000) {
    Serial.print("📡 Sent ");
    Serial.print(packetCount);
    Serial.print(" packets to XIAO. Trial active: ");
    Serial.print(trialActive ? "YES" : "NO");
    Serial.print(", Angular vel: ");
    Serial.println(data.angular_velocity, 2);
    lastDebugTime = millis();
    packetCount = 0;
  }
}

// Initialize file counter by checking existing files on SD card
void initializeFileCounter() {
  fileCounter = 1;

  // Check for existing files and find the next available number
  while (true) {
    String filename = "trial_" + String(fileCounter) + ".csv";
    if (!SD.exists(filename)) {
      break; // Found an available filename
    }
    fileCounter++;

    // Safety check to prevent infinite loop
    if (fileCounter > 9999) {
      Serial.println("Warning: Too many files on SD card, starting from trial_1.csv");
      fileCounter = 1;
      break;
    }
  }

  Serial.println("Next file will be: trial_" + String(fileCounter) + ".csv");
}

// Handle button input with debouncing
void handleButtonInput() {
  bool currentButtonState = digitalRead(BUTTON_PIN);

  // Check if button state changed
  if (currentButtonState != lastButtonState) {
    lastDebounceTime = millis();
  }

  // If enough time has passed since last state change
  if ((millis() - lastDebounceTime) > debounceDelay) {
    // If button state has changed and is now pressed (LOW due to pullup)
    if (currentButtonState == LOW && !buttonPressed) {
      buttonPressed = true;
      toggleTrial();
    } else if (currentButtonState == HIGH) {
      buttonPressed = false;
    }
  }

  lastButtonState = currentButtonState;
}

// Toggle trial recording on/off
void toggleTrial() {
  if (!trialActive) {
    // Start new trial
    startTrial();
  } else {
    // End current trial
    endTrial();
  }
}

// Start a new trial
void startTrial() {
  trialActive = true;

  // Create new filename
  currentFilename = "trial_" + String(fileCounter) + ".csv";

  // Create file with CSV header
  File file = SD.open(currentFilename, FILE_WRITE);
  if (file) {
    file.println("Timestamp,AngularVel,AngularVelX,AngularVelY,AngularVelZ,AccelX,AccelY,AccelZ,Roll,Pitch,Yaw");
    file.close();
    Serial.println("✅ Started trial recording: " + currentFilename);
  } else {
    Serial.println("❌ Failed to create trial file: " + currentFilename);
    trialActive = false;
    return;
  }

  // Set LED to solid (trial active)
  digitalWrite(LED_PIN, HIGH);
  ledState = true;
}

// End current trial
void endTrial() {
  trialActive = false;

  Serial.println("✅ Ended trial recording: " + currentFilename);
  Serial.println("Data saved to SD card");

  // Increment file counter for next trial
  fileCounter++;
  currentFilename = "";

  // LED will start flashing again (handled in handleLedIndication)
}

// Handle LED indication based on trial state
void handleLedIndication() {
  if (trialActive) {
    // Trial active - LED should be solid
    if (!ledState) {
      digitalWrite(LED_PIN, HIGH);
      ledState = true;
    }
  } else {
    // Trial inactive - LED should flash
    if (millis() - lastLedToggle >= ledFlashInterval) {
      ledState = !ledState;
      digitalWrite(LED_PIN, ledState ? HIGH : LOW);
      lastLedToggle = millis();
    }
  }
}
