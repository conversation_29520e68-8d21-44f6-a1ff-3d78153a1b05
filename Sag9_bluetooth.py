import dash
from dash import dcc, html
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
from collections import deque
import time
import random
import threading
import csv  
import signal
import sys
import numpy as np
import asyncio
from bleak import BleakClient, BleakScanner
import struct

# BLE Configuration
DEVICE_NAME = "XIAO-SensorHub"
SERVICE_UUID = "12345678-1234-1234-1234-123456789abc"
CHARACTERISTIC_UUID = "*************-4321-4321-cba987654321"

# BLE connection variables
ble_client = None
ble_connected = False
mock = False  # Set to True for simulated data

# Define units for each sensor
sensor_units = {
    "Angular Velocity": "deg/s",
    "Angular Velocity X SD": "deg/s",
    "Angular Velocity Y SD": "deg/s",
    "Angular Velocity Z SD": "deg/s",
    "Accelerometer X": "m/s²",
    "Accelerometer Y": "m/s²",
    "Accelerometer Z": "m/s²",
    "Roll": "degrees",
    "Pitch": "degrees", 
    "Yaw": "degrees",
}

# Define mapping from sensor names to graph IDs
graph_ids = {
    "Angular Velocity": "graph-angular-velocity",
    "Angular Velocity X SD": "graph-angular-velocity-x-sd",
    "Angular Velocity Y SD": "graph-angular-velocity-y-sd",
    "Angular Velocity Z SD": "graph-angular-velocity-z-sd",
    "Accelerometer X": "graph-accelerometer-x",
    "Accelerometer Y": "graph-accelerometer-y",
    "Accelerometer Z": "graph-accelerometer-z",
    "Roll": "graph-roll",
    "Pitch": "graph-pitch",
    "Yaw": "graph-yaw",
}

# Data storage
max_points = 1000
time_data = deque(maxlen=max_points)
streams = {
    "Angular Velocity": deque(maxlen=max_points),
    "Angular Velocity X SD": deque(maxlen=max_points),
    "Angular Velocity Y SD": deque(maxlen=max_points),
    "Angular Velocity Z SD": deque(maxlen=max_points),
    "Accelerometer X": deque(maxlen=max_points),
    "Accelerometer Y": deque(maxlen=max_points),
    "Accelerometer Z": deque(maxlen=max_points),
    "Roll": deque(maxlen=max_points),
    "Pitch": deque(maxlen=max_points),
    "Yaw": deque(maxlen=max_points),
}

# Storage for orientation data
current_roll = 0
current_pitch = 0
current_yaw = 0
roll_history = deque(maxlen=100)
pitch_history = deque(maxlen=100)
yaw_history = deque(maxlen=100)

# Trial recording system
trial_recording = False
trial_data = []
trial_start_time = None

saved_data = []
running = True
data_thread = None
app = dash.Dash(__name__)

def signal_handler(sig, frame):
    """Handle keyboard interrupts (Ctrl+C)"""
    print("\nReceived keyboard interrupt, stopping gracefully...")
    stop()
    sys.exit(0)

async def scan_for_device():
    """Scan for the XIAO BLE device"""
    print("🔍 Scanning for XIAO-SensorHub...")
    devices = await BleakScanner.discover()
    
    for device in devices:
        if device.name == DEVICE_NAME:
            print(f"✅ Found {DEVICE_NAME} at {device.address}")
            return device.address
    
    print(f"❌ {DEVICE_NAME} not found")
    return None

async def connect_ble():
    """Initialize BLE connection. Returns True if successful, False otherwise."""
    global ble_client, ble_connected
    
    try:
        device_address = await scan_for_device()
        if not device_address:
            return False
        
        ble_client = BleakClient(device_address)
        await ble_client.connect()
        
        if ble_client.is_connected:
            print("✅ BLE connection established.")
            ble_connected = True
            
            # Start notifications
            await ble_client.start_notify(CHARACTERISTIC_UUID, ble_notification_handler)
            return True
        else:
            print("❌ Failed to connect to BLE device")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to BLE device: {e}")
        ble_client = None
        ble_connected = False
        return False

def ble_notification_handler(sender, data):
    """Handle incoming BLE notifications"""
    try:
        if len(data) >= 20:
            # Unpack the data packet
            # Format: header1, header2, timestamp(4), ang_vel(2), ang_vel_x(2), ang_vel_y(2), ang_vel_z(2), accel_x(2), accel_y(2), accel_z(2)
            header1, header2 = struct.unpack('BB', data[0:2])
            
            if header1 == 0xAA and header2 == 0xAA:
                timestamp = struct.unpack('>I', data[2:6])[0]  # Big-endian 32-bit unsigned int
                
                # Unpack sensor data (big-endian 16-bit signed integers)
                sensor_data = struct.unpack('>7h', data[6:20])  # 7 signed 16-bit integers
                
                # Convert back to float values (divided by 100 as they were multiplied by 100)
                angular_velocity = sensor_data[0] / 100.0
                angular_velocity_x = sensor_data[1] / 100.0
                angular_velocity_y = sensor_data[2] / 100.0
                angular_velocity_z = sensor_data[3] / 100.0
                accelerometer_x = sensor_data[4] / 100.0
                accelerometer_y = sensor_data[5] / 100.0
                accelerometer_z = sensor_data[6] / 100.0
                
                processed_data = [
                    timestamp,
                    angular_velocity,
                    angular_velocity_x,
                    angular_velocity_y,
                    angular_velocity_z,
                    accelerometer_x,
                    accelerometer_y,
                    accelerometer_z
                ]
                
                process_data(processed_data)
                
    except Exception as e:
        print(f"Error processing BLE data: {e}")

def convert_to_signed_int(unsigned_value):
    """Convert 16-bit unsigned integer to signed integer."""
    if unsigned_value <= 0x7FFF:
        return unsigned_value
    return unsigned_value - 0x10000

def mock_serial_read():
    """Generate mock sensor data for testing."""
    runtime = len(time_data) + 1 if time_data else 0
    
    time_factor = runtime / 10.0
    
    # Generate realistic boat motion
    roll_primary = 15 * np.sin(time_factor * 0.1)
    roll_secondary = 5 * np.sin(time_factor * 0.25)
    pitch_primary = 10 * np.sin(time_factor * 0.08 + 1.5)
    pitch_secondary = 3 * np.sin(time_factor * 0.2)
    yaw_motion = 8 * np.sin(time_factor * 0.05)
    
    roll = roll_primary + roll_secondary
    pitch = pitch_primary + pitch_secondary
    yaw = yaw_motion
    
    # Convert to radians for calculations
    roll_rad = np.radians(roll)
    pitch_rad = np.radians(pitch)
    g = 9.81
    
    # Generate angular velocities
    angular_velocity = random.uniform(-50, 50)
    angular_velocity_x_sd = random.uniform(-30, 30)
    angular_velocity_y_sd = random.uniform(-30, 30)
    angular_velocity_z_sd = random.uniform(-30, 30)
    
    # Calculate gravity components
    accelerometer_x = np.sin(roll_rad) * g + random.uniform(-0.2, 0.2)
    accelerometer_y = np.sin(pitch_rad) * g + random.uniform(-0.2, 0.2)
    accelerometer_z = np.cos(roll_rad) * np.cos(pitch_rad) * g + random.uniform(-0.2, 0.2)
    
    # Update orientation history
    roll_history.append(roll)
    pitch_history.append(pitch)
    yaw_history.append(yaw)
    
    global current_roll, current_pitch, current_yaw
    current_roll = roll
    current_pitch = pitch
    current_yaw = yaw
    
    data = [
        runtime,
        angular_velocity,
        angular_velocity_x_sd,
        angular_velocity_y_sd,
        angular_velocity_z_sd,
        accelerometer_x,
        accelerometer_y,
        accelerometer_z
    ]
    
    process_data(data)
    time.sleep(0.1)

def process_data(data):
    """Process the incoming data and update data structures."""
    if not running:
        return

    global current_roll, current_pitch, current_yaw

    if len(data) >= 8:
        accel_x = data[5]
        accel_y = data[6]
        accel_z = data[7]

        if not mock:
            # Calculate roll and pitch from accelerometer data
            if abs(accel_z) > 0.1:
                roll = np.degrees(np.arctan2(accel_y, accel_z))
                current_roll = 0.1 * roll + 0.9 * current_roll
                roll_history.append(current_roll)

            if abs(accel_z) > 0.1:
                pitch = np.degrees(np.arctan2(-accel_x, accel_z))
                current_pitch = 0.1 * pitch + 0.9 * current_pitch
                pitch_history.append(current_pitch)

            # For yaw, we'd need magnetometer data, so use angular velocity Z for now
            yaw_rate = data[4] if len(data) > 4 else 0
            current_yaw += yaw_rate * 0.01  # Simple integration
            yaw_history.append(current_yaw)

    # Save to permanent storage
    saved_data.append(data)

    # Save to trial data if recording
    if trial_recording:
        trial_data.append(data)

    # Update live data
    time_data.append(data[0])
    for i, key in enumerate(list(streams.keys())[:7]):  # First 7 are from sensor data
        if i + 1 < len(data):
            streams[key].append(data[i + 1])
        else:
            streams[key].append(0)

    # Update orientation data
    streams["Roll"].append(current_roll)
    streams["Pitch"].append(current_pitch)
    streams["Yaw"].append(current_yaw)

async def ble_reader_thread():
    """Thread for managing BLE connection."""
    global mock, ble_client, ble_connected

    while running:
        try:
            if not ble_connected:
                print("Attempting to connect to BLE device...")
                if await connect_ble():
                    print("✅ BLE connected successfully")
                else:
                    print("❌ BLE connection failed, switching to mock mode")
                    mock = True
                    break

            # Keep connection alive
            if ble_client and ble_client.is_connected:
                await asyncio.sleep(1)
            else:
                print("BLE connection lost")
                ble_connected = False

        except Exception as e:
            if running:
                print(f"Error in BLE thread: {e}")
                ble_connected = False
                await asyncio.sleep(1)

def mock_reader_thread():
    """Thread for generating mock data."""
    while running:
        try:
            if not mock:
                print("Switched to real data mode. Exiting mock reader thread.")
                return
            mock_serial_read()
        except Exception as e:
            if running:
                print(f"Error in mock data thread: {e}")
                time.sleep(1)

def start_trial():
    """Start recording trial data."""
    global trial_recording, trial_data, trial_start_time
    trial_recording = True
    trial_data = []
    trial_start_time = time.time()
    print("Trial recording started")

def stop_trial():
    """Stop recording trial data and save to file."""
    global trial_recording, trial_data, trial_start_time

    if not trial_recording:
        print("No trial currently recording")
        return None

    trial_recording = False

    try:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"trial_data_{timestamp}.csv"

        with open(filename, "w", newline='') as file:
            writer = csv.writer(file)
            header = ["Runtime", "Angular Velocity", "Angular Velocity X SD",
                     "Angular Velocity Y SD", "Angular Velocity Z SD",
                     "Accelerometer X", "Accelerometer Y", "Accelerometer Z"]
            writer.writerow(header)
            for data in trial_data:
                writer.writerow(data)

        duration = time.time() - trial_start_time
        print(f"Trial data saved to {filename} (Duration: {duration:.1f}s, Points: {len(trial_data)})")
        return filename
    except Exception as e:
        print(f"Error saving trial data: {e}")
        return None

def save_to_file():
    """Saves all collected data to a CSV file."""
    try:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"data_log_{timestamp}.csv"

        with open(filename, "w", newline='') as file:
            writer = csv.writer(file)
            header = ["Runtime"] + list(streams.keys())[:7]  # First 7 sensor streams
            writer.writerow(header)
            for data in saved_data:
                writer.writerow(data)
        print(f"Data saved to {filename}")
        return filename
    except Exception as e:
        print(f"Error saving data: {e}")
        return None

async def stop():
    """Stops the program gracefully."""
    global running, ble_client, ble_connected
    if not running:
        print("Already stopped.")
        return

    print("Stopping data collection...")
    running = False

    # Disconnect BLE
    if ble_client and ble_connected:
        try:
            await ble_client.disconnect()
            print("BLE connection closed.")
        except Exception as e:
            print(f"Error closing BLE connection: {e}")

    time.sleep(0.5)

    filename = save_to_file()
    if filename:
        print(f"Program stopped gracefully. Data saved to {filename}")
        return filename
    else:
        print("Program stopped, but data could not be saved.")
        return None

def create_attitude_indicator(sensor, current_value):
    """Create an aircraft-style attitude indicator for orientation sensors."""
    fig = go.Figure()

    if sensor == "Roll":
        # Roll indicator - circular with bank angle markings
        theta = np.linspace(0, 2*np.pi, 360)
        circle_x = np.cos(theta)
        circle_y = np.sin(theta)

        # Add outer circle
        fig.add_trace(go.Scatter(
            x=circle_x, y=circle_y,
            mode='lines',
            line=dict(color='white', width=3),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add bank angle markings (0, 10, 20, 30, 45, 60, 90 degrees)
        bank_angles = [0, 10, 20, 30, 45, 60, 90]
        for angle in bank_angles:
            for side in [-1, 1]:  # Both sides
                rad = np.radians(side * angle)
                if abs(angle) in [0, 30, 60, 90]:
                    # Major markings
                    inner_r, outer_r = 0.85, 1.0
                    width = 3
                else:
                    # Minor markings
                    inner_r, outer_r = 0.9, 1.0
                    width = 2

                x_vals = [inner_r * np.sin(rad), outer_r * np.sin(rad)]
                y_vals = [inner_r * np.cos(rad), outer_r * np.cos(rad)]

                fig.add_trace(go.Scatter(
                    x=x_vals, y=y_vals,
                    mode='lines',
                    line=dict(color='white', width=width),
                    showlegend=False,
                    hoverinfo='skip'
                ))

                # Add labels for major angles
                if abs(angle) in [30, 60, 90] and side == 1:
                    fig.add_annotation(
                        x=1.15 * np.sin(rad),
                        y=1.15 * np.cos(rad),
                        text=str(abs(angle)),
                        showarrow=False,
                        font=dict(size=12, color='white'),
                        xanchor='center',
                        yanchor='middle'
                    )

        # Add aircraft symbol (triangle at center)
        aircraft_x = [-0.15, 0, 0.15, -0.15]
        aircraft_y = [0, 0.1, 0, 0]
        fig.add_trace(go.Scatter(
            x=aircraft_x, y=aircraft_y,
            mode='lines',
            line=dict(color='yellow', width=4),
            fill='toself',
            fillcolor='yellow',
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add roll pointer
        roll_rad = np.radians(-current_value)
        pointer_x = [0.75 * np.sin(roll_rad), 0.85 * np.sin(roll_rad)]
        pointer_y = [0.75 * np.cos(roll_rad), 0.85 * np.cos(roll_rad)]
        fig.add_trace(go.Scatter(
            x=pointer_x, y=pointer_y,
            mode='lines',
            line=dict(color='red', width=4),
            showlegend=False,
            hovertemplate=f'Roll: {current_value:.1f}°<extra></extra>'
        ))

        bg_color = 'rgba(0,50,100,0.9)'

    elif sensor == "Pitch":
        # Pitch indicator - horizontal lines with degree markings
        # Create horizon line
        fig.add_trace(go.Scatter(
            x=[-1, 1], y=[0, 0],
            mode='lines',
            line=dict(color='white', width=4),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add pitch lines every 10 degrees
        for pitch_angle in range(-90, 91, 10):
            if pitch_angle == 0:
                continue  # Skip horizon line

            y_pos = pitch_angle / 90.0 * 0.8  # Scale to fit in view
            line_width = 0.6 if abs(pitch_angle) % 30 == 0 else 0.3

            fig.add_trace(go.Scatter(
                x=[-line_width, line_width], y=[y_pos, y_pos],
                mode='lines',
                line=dict(color='white', width=2),
                showlegend=False,
                hoverinfo='skip'
            ))

            # Add degree labels
            if abs(pitch_angle) % 30 == 0:
                fig.add_annotation(
                    x=line_width + 0.1,
                    y=y_pos,
                    text=f"{abs(pitch_angle)}°",
                    showarrow=False,
                    font=dict(size=10, color='white'),
                    xanchor='left',
                    yanchor='middle'
                )

        # Add aircraft symbol
        fig.add_trace(go.Scatter(
            x=[-0.3, -0.1, 0, 0.1, 0.3],
            y=[0, 0, 0.05, 0, 0],
            mode='lines',
            line=dict(color='yellow', width=4),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add pitch indicator
        pitch_y = current_value / 90.0 * 0.8
        fig.add_trace(go.Scatter(
            x=[0], y=[pitch_y],
            mode='markers',
            marker=dict(size=15, color='red', symbol='triangle-up'),
            showlegend=False,
            hovertemplate=f'Pitch: {current_value:.1f}°<extra></extra>'
        ))

        bg_color = 'rgba(100,50,0,0.9)' if current_value > 0 else 'rgba(0,50,100,0.9)'

    else:  # Yaw
        # Yaw indicator - compass-style
        theta = np.linspace(0, 2*np.pi, 360)
        circle_x = np.cos(theta)
        circle_y = np.sin(theta)

        # Add outer circle
        fig.add_trace(go.Scatter(
            x=circle_x, y=circle_y,
            mode='lines',
            line=dict(color='white', width=3),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add compass markings every 30 degrees
        compass_labels = ['N', '30', '60', 'E', '120', '150', 'S', '210', '240', 'W', '300', '330']
        for i, (deg, label) in enumerate(zip(range(0, 360, 30), compass_labels)):
            rad = np.radians(-deg + 90)  # North at top

            # Major/minor markings
            if deg % 90 == 0:  # Cardinal directions
                inner_r, outer_r = 0.8, 1.0
                width = 3
            else:
                inner_r, outer_r = 0.85, 1.0
                width = 2

            x_vals = [inner_r * np.cos(rad), outer_r * np.cos(rad)]
            y_vals = [inner_r * np.sin(rad), outer_r * np.sin(rad)]

            fig.add_trace(go.Scatter(
                x=x_vals, y=y_vals,
                mode='lines',
                line=dict(color='white', width=width),
                showlegend=False,
                hoverinfo='skip'
            ))

            # Add labels
            fig.add_annotation(
                x=1.15 * np.cos(rad),
                y=1.15 * np.sin(rad),
                text=label,
                showarrow=False,
                font=dict(size=12, color='white'),
                xanchor='center',
                yanchor='middle'
            )

        # Add heading pointer
        heading_rad = np.radians(-current_value + 90)
        pointer_x = [0, 0.7 * np.cos(heading_rad)]
        pointer_y = [0, 0.7 * np.sin(heading_rad)]
        fig.add_trace(go.Scatter(
            x=pointer_x, y=pointer_y,
            mode='lines',
            line=dict(color='red', width=4),
            showlegend=False,
            hovertemplate=f'Yaw: {current_value:.1f}°<extra></extra>'
        ))

        # Add center dot
        fig.add_trace(go.Scatter(
            x=[0], y=[0],
            mode='markers',
            marker=dict(size=8, color='white'),
            showlegend=False,
            hoverinfo='skip'
        ))

        bg_color = 'rgba(0,0,0,0.9)'

    # Add current value text
    fig.add_annotation(
        x=0, y=-1.4,
        text=f"{sensor}: {current_value:.1f}°",
        showarrow=False,
        font=dict(size=14, color='white', family="Arial Black"),
        xanchor='center',
        yanchor='middle'
    )

    fig.update_layout(
        xaxis=dict(
            range=[-1.5, 1.5],
            showgrid=False,
            showticklabels=False,
            zeroline=False,
            fixedrange=True
        ),
        yaxis=dict(
            range=[-1.5, 1.5],
            showgrid=False,
            showticklabels=False,
            zeroline=False,
            scaleanchor="x",
            scaleratio=1,
            fixedrange=True
        ),
        plot_bgcolor=bg_color,
        paper_bgcolor='rgba(0,0,0,0)',
        margin=dict(l=20, r=20, t=20, b=40),
        height=300,
        width=300,
        autosize=False,
        uirevision=sensor
    )

    return fig

def create_figure(sensor):
    """Create a Plotly figure for the given sensor."""
    # Use attitude indicator for orientation sensors
    if sensor in ["Roll", "Pitch", "Yaw"]:
        current_value = 0
        if sensor == "Roll":
            current_value = current_roll
        elif sensor == "Pitch":
            current_value = current_pitch
        elif sensor == "Yaw":
            current_value = current_yaw
        return create_attitude_indicator(sensor, current_value)

    # Regular line plots for other sensors
    color_map = {
        "Angular Velocity": "#2980b9",
        "Angular Velocity X SD": "#c0392b",
        "Angular Velocity Y SD": "#d35400",
        "Angular Velocity Z SD": "#e74c3c",
        "Accelerometer X": "#27ae60",
        "Accelerometer Y": "#2ecc71",
        "Accelerometer Z": "#16a085",
    }

    color = color_map.get(sensor, "#2980b9")

    return {
        'data': [
            {
                'x': list(time_data),
                'y': list(streams[sensor]),
                'type': 'scatter',
                'mode': 'lines',
                'name': sensor,
                'line': {'color': color, 'width': 2}
            }
        ],
        'layout': {
            'xaxis': {
                'title': 'Time (ms)',
                'tickformat': ',d'
            },
            'yaxis': {
                'title': f'Value ({sensor_units[sensor]})',
                'tickformat': '.2f'
            },
            'margin': {'l': 50, 'r': 20, 't': 10, 'b': 50},
            'height': 300,
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'plot_bgcolor': 'rgba(240,240,240,0.8)',
            'hovermode': 'closest',
            'uirevision': sensor,
        }
    }

def setup_layout():
    """Set up the Dash app layout."""
    app.layout = html.Div([
        html.H1("Bluetooth IMU Sensor Data Visualization with Trial Recording",
                style={'textAlign': 'center', 'color': '#2c3e50', 'margin-bottom': '30px'}),

        # Dashboard information
        html.Div([
            html.H3("Dashboard Information", style={'color': '#2c3e50'}),
            html.Div([
                html.P(f"Sensor: {sensor} ({unit})", style={'margin': '5px 0'})
                for sensor, unit in sensor_units.items()
            ]),
            html.P(f"Data Source: {'Mock Data (Simulated)' if mock else 'Bluetooth XIAO Sensor'}",
                  style={'fontWeight': 'bold', 'marginTop': '10px', 'color': '#e74c3c' if mock else '#27ae60'})
        ], style={'marginBottom': '20px', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '5px'}),

        # Trial Controls
        html.Div([
            html.H2("Trial Recording", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),
            html.Div([
                html.Button('Start Trial', id='start-trial-button',
                            style={'margin': '10px', 'padding': '15px 30px', 'backgroundColor': '#27ae60', 'color': 'white', 'border': 'none', 'borderRadius': '5px', 'fontSize': '16px'}),
                html.Button('Stop Trial', id='stop-trial-button',
                            style={'margin': '10px', 'padding': '15px 30px', 'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'borderRadius': '5px', 'fontSize': '16px'}),
                html.Div(id='trial-status', style={'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold'})
            ], style={'display': 'flex', 'flexWrap': 'wrap', 'justifyContent': 'center', 'alignItems': 'center'}),
        ], style={'marginBottom': '20px', 'backgroundColor': '#e8f5e8', 'padding': '15px', 'borderRadius': '5px'}),

        # Orientation Graphs (Roll, Pitch, Yaw)
        html.Div([
            html.H2("Orientation Data", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),
            html.Div([
                html.Div([
                    html.H3(f"Roll ({sensor_units['Roll']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Roll'], style={'height': '300px'})
                ], style={'width': '33%', 'padding': '10px', 'display': 'inline-block', 'verticalAlign': 'top'}),

                html.Div([
                    html.H3(f"Pitch ({sensor_units['Pitch']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Pitch'], style={'height': '300px'})
                ], style={'width': '33%', 'padding': '10px', 'display': 'inline-block', 'verticalAlign': 'top'}),

                html.Div([
                    html.H3(f"Yaw ({sensor_units['Yaw']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Yaw'], style={'height': '300px'})
                ], style={'width': '33%', 'padding': '10px', 'display': 'inline-block', 'verticalAlign': 'top'}),
            ], style={'textAlign': 'center', 'whiteSpace': 'nowrap'}),
        ], style={'marginBottom': '20px'}),

        # Sensor Data Graphs
        html.Div([
            html.H2("Sensor Data", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),

            # Angular Velocity graphs
            html.Div([
                html.Div([
                    html.H3(f"Angular Velocity ({sensor_units['Angular Velocity']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity'], style={'height': '300px'})
                ], style={'width': '25%', 'padding': '10px', 'minHeight': '350px'}),

                html.Div([
                    html.H3(f"Angular Velocity X SD ({sensor_units['Angular Velocity X SD']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity X SD'], style={'height': '300px'})
                ], style={'width': '25%', 'padding': '10px', 'minHeight': '350px'}),

                html.Div([
                    html.H3(f"Angular Velocity Y SD ({sensor_units['Angular Velocity Y SD']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Y SD'], style={'height': '300px'})
                ], style={'width': '25%', 'padding': '10px', 'minHeight': '350px'}),

                html.Div([
                    html.H3(f"Angular Velocity Z SD ({sensor_units['Angular Velocity Z SD']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Z SD'], style={'height': '300px'})
                ], style={'width': '25%', 'padding': '10px', 'minHeight': '350px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap', 'marginBottom': '20px'}),

            # Accelerometer graphs
            html.Div([
                html.Div([
                    html.H3(f"Accelerometer X ({sensor_units['Accelerometer X']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer X'], style={'height': '300px'})
                ], style={'width': '33.33%', 'padding': '10px', 'minHeight': '350px'}),

                html.Div([
                    html.H3(f"Accelerometer Y ({sensor_units['Accelerometer Y']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Y'], style={'height': '300px'})
                ], style={'width': '33.33%', 'padding': '10px', 'minHeight': '350px'}),

                html.Div([
                    html.H3(f"Accelerometer Z ({sensor_units['Accelerometer Z']})",
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Z'], style={'height': '300px'})
                ], style={'width': '33.33%', 'padding': '10px', 'minHeight': '350px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap', 'marginBottom': '20px'}),
        ], style={'marginBottom': '20px'}),

        # Data refresh interval
        dcc.Interval(
            id='interval-component',
            interval=100,
            n_intervals=0
        )
    ])

# Callbacks
@app.callback(
    [Output(graph_id, 'figure') for graph_id in graph_ids.values()],
    [Input('interval-component', 'n_intervals')]
)
def update_graphs(n):
    """Update all graphs with new data."""
    return [create_figure(sensor) for sensor in graph_ids.keys()]

@app.callback(
    [Output('trial-status', 'children'),
     Output('trial-status', 'style')],
    [Input('start-trial-button', 'n_clicks'),
     Input('stop-trial-button', 'n_clicks')],
    [State('trial-status', 'children')]
)
def handle_trial_buttons(start_clicks, stop_clicks, current_message):
    """Handle trial recording button presses."""
    ctx = dash.callback_context
    if not ctx.triggered:
        if trial_recording:
            return "🔴 Trial Recording in Progress", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#27ae60', 'backgroundColor': '#d4edda', 'borderRadius': '5px'}
        else:
            return "⚪ No Active Trial", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#6c757d'}

    button_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if button_id == 'start-trial-button' and start_clicks:
        if not trial_recording:
            start_trial()
            return f"🔴 Trial Recording Started", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#27ae60', 'backgroundColor': '#d4edda', 'borderRadius': '5px'}
        else:
            return "🔴 Trial Already Recording", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#f39c12', 'backgroundColor': '#fff3cd', 'borderRadius': '5px'}

    elif button_id == 'stop-trial-button' and stop_clicks:
        if trial_recording:
            filename = stop_trial()
            if filename:
                return f"✅ Trial Stopped - Data saved to {filename}", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#155724', 'backgroundColor': '#d4edda', 'borderRadius': '5px'}
            else:
                return "❌ Trial Stopped - Error saving data", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#721c24', 'backgroundColor': '#f8d7da', 'borderRadius': '5px'}
        else:
            return "⚪ No Active Trial to Stop", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#f39c12', 'backgroundColor': '#fff3cd', 'borderRadius': '5px'}

    return current_message, {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold'}

async def main():
    """Main async function to handle BLE and dashboard"""
    global mock, data_thread, running

    signal.signal(signal.SIGINT, signal_handler)

    # Try to connect to BLE device
    if not mock:
        print("Attempting to connect to BLE device...")
        if not await connect_ble():
            print("Failed to connect to BLE device. Switching to mock data mode.")
            mock = True

    setup_layout()

    running = True
    if not mock:
        # Start BLE reader in a separate thread
        def run_ble_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(ble_reader_thread())

        data_thread = threading.Thread(target=run_ble_loop, daemon=True)
    else:
        data_thread = threading.Thread(target=mock_reader_thread, daemon=True)

    data_thread.start()

    print("Starting Dash application...")
    app.run(debug=True, use_reloader=False)

if __name__ == '__main__':
    # Run the async main function
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
        running = False
